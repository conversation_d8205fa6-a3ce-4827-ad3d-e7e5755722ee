import * as React from 'react';
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from 'src/lib/components/ui/command';
import { Input } from 'src/lib/components/ui/input';
import { cn } from 'src/lib/utils';

interface AutocompleteInputProps {
  suggestions: string[];
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
  defaultValue?: string;
  id?: string;
}

export function AutocompleteInput({
  suggestions,
  placeholder,
  className,
  onChange,
  defaultValue = '',
  id,
}: AutocompleteInputProps) {
  const [value, setValue] = React.useState(defaultValue);
  const [showSuggestions, setShowSuggestions] = React.useState(false);
  const [commandValue, setCommandValue] = React.useState('');
  const inputRef = React.useRef<HTMLInputElement>(null);
  const commandRef = React.useRef<HTMLDivElement>(null);

  const filteredSuggestions = React.useMemo(() => {
    if (!value) return suggestions;

    return suggestions.filter(suggestion => suggestion.toLowerCase().includes(value.toLowerCase()));
  }, [value, suggestions]);

  // Handle clicks outside to close suggestions
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        commandRef.current &&
        !commandRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);

    if (onChange) {
      onChange(newValue);
    }

    // Show suggestions when typing
    if (filteredSuggestions.length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionSelect = (selectedValue: string) => {
    setValue(selectedValue);
    if (onChange) {
      onChange(selectedValue);
    }
    inputRef.current?.focus();
    setShowSuggestions(false);
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (commandRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }
    setShowSuggestions(false);
  };

  return (
    <div className="relative w-full">
      <Input
        ref={inputRef}
        id={id}
        value={value}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        placeholder={placeholder}
        className={cn('w-full', className)}
        onFocus={() => {
          if (filteredSuggestions.length > 0) {
            setShowSuggestions(true);
          }
        }}
      />

      {showSuggestions && (
        <div
          ref={commandRef}
          className="absolute z-50 w-full mt-1 overflow-hidden bg-popover text-popover-foreground rounded-md border shadow-md"
        >
          <Command className="w-full">
            <CommandList>
              <CommandEmpty></CommandEmpty>
              <CommandGroup>
                {filteredSuggestions.map(suggestion => (
                  <CommandItem
                    key={suggestion}
                    onSelect={() => handleSuggestionSelect(suggestion)}
                    className="cursor-pointer"
                  >
                    {suggestion}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
}
